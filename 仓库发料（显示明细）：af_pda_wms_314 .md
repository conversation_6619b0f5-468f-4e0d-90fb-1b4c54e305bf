```sql
-- DROP FUNCTION public.af_pda_wms_314(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_314(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：仓库发料（生产领料）
 * 描述：
 * 时间：
 * 开发者：
 */
	declare 
		jsondatas json;
		_user_no text;
		_user_id text;
		_user_name text;
	
		_mtr_use_no text;
		_sn_no text;
		_part_no text;
		_part_name text;
		_part_qty_tm numeric;
	
		_mtr_use_h_id text;
		_part_lot_no text;
		_mrp_no_mo text;
		_mrp_no_part text;
		_part_qty_req numeric;
		_part_qty_real numeric;
		_part_qty_send numeric;
		_part_qty_wm numeric;
		_djscd text;
		_mo_no text;
		_unode text;
		_invp_no text;
		_prod_stockin_dt timestamp;
		_new_sn_no text;
		_part_data_precision text;
		_tm_type text;
		tmpjson json[];
		_part_qty_kc numeric;
		_part_qty_w numeric;
		_part_qty_jc numeric;
	    _part_qty_sf numeric;
		_part_qty_real_2 numeric;
	
		_io_stock_name text;
		_io_stock_type text;
		_part_qty_cf	numeric;
		_part_qty_count	numeric;
		_sn_pack_40		text;
		_me_mtr_use_h_dept_no text;
		_me_mtr_use_h_sap_type	text;
		
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
	    _part_sn_no_tmp text;
	    _ea_no text;
	    _ea_name text;
	    _workshop_no text;
	    _send_produce_date text;
	    _min_produce_date text;
	    _min_sn_invp_no text;
	    _yx_sn_no text;
	    _supplier_no text;
	
		res returntype;
	begin
		jsondatas := json(datas);
		_user_no := jsondatas->>'user_no';
	
		jsondatas := json(jsondatas->'datas');
		raise notice '%',jsondatas;

		----移到af_pda_wms_314a  20220113------------------
		for iCount in 0..json_array_length(jsondatas)-1 loop
			--_mtr_use_no := jsondatas->iCount->>'mtr_use_no';
			_mtr_use_no := jsondatas->iCount->>'mr_no';
			_sn_no := jsondatas->iCount->>'sn_no';
			_supplier_no := jsondatas->iCount->>'supplier_no';
			_part_qty_tm := coalesce(jsondatas->iCount->>'part_qty')::numeric;
			_part_qty_send := coalesce(jsondatas->iCount->>'part_qty_send')::numeric;
			--_part_no := jsondatas->iCount->>'part_no'; 
			_part_name := jsondatas->iCount->>'part_name';

			select upper(part_no) as part_no,lot_no,wm_sn_rmk06,invp_no,upd_time into _part_no,_part_lot_no,_mrp_no_part,_invp_no,_prod_stockin_dt from wm_sn where sn_no =_sn_no;
			select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
		
			select awit.io_type_name,mm.me_mtr_use_h_io_type,mm.me_mtr_use_h_dept_no  into _io_stock_name,_io_stock_type,_me_mtr_use_h_dept_no
			from me_mtr_use_h mm left join av_wm_io_type awit on awit.io_type_no=mm.me_mtr_use_h_io_type
			where mm.me_mtr_use_h_no=_mtr_use_no;
		
			--获取领料单线别
			select bb.me_mtr_use_b_rmk12,bb.me_mtr_use_b_rmk12,mm.me_mtr_use_h_sap_type  into _ea_no,_ea_name,_me_mtr_use_h_sap_type  
			from me_mtr_use_h mm 
			left join me_mtr_use_b bb on mm.me_mtr_use_h_id = bb.me_mtr_use_h_id 
			where mm.me_mtr_use_h_no=_mtr_use_no
		    limit 1;
		   --获取归属车间
		   
		   select e.ea_type3 into _workshop_no from ea e where e.ea_no ||'   '||e.ea_name =_ea_no ;
		  
		  --如果车间值为空，就让它等于产线
		  if (_workshop_no is null or _workshop_no = '') then 
		  	_workshop_no := _ea_no;
		  end if;
		  
		 ---非生产领料单扣账，线别与车间=null 则赋值为部门编号
		 if (_ea_no is null or _ea_no = '') then
		 	_ea_no := _me_mtr_use_h_dept_no;
		 	_ea_name := _me_mtr_use_h_dept_no;
		 	if (_workshop_no is null or _workshop_no = '') then 
		  		_workshop_no := _me_mtr_use_h_dept_no;
		    end if;
		 end if;
		 
		--增加条码已领料扫描校验 xzp
				 if  exists(select 1 	from me_mtr_use_sn_part a , me_mtr_use_h h
							where a.me_mtr_use_h_id = h.me_mtr_use_h_id 
							and h.me_mtr_use_h_io_type ='0'
							and a.sn_no = _sn_no and h.me_mtr_use_h_no = _mtr_use_no
			)   then 
		    		 res:=row('false','条码'||_sn_no||'在领料单已发投料扫描，不能再次发投料扫描！');
				return  to_json(res);  
		   end if; 
  
			--add 2022-04-23 增加检查单据状态		
			if   exists( select 1 from me_mtr_use_h where me_mtr_use_h_status not in ('200','210') and me_mtr_use_h_no =_mtr_use_no ) then
				res := row('false','此生产领料单: '||_mtr_use_no||'，状态为已确认，不允许再扫码发料');
				return to_json(res);
			end if;

		--品号需求数量
			select  coalesce (sum(mmub.part_qty_req),0) into _part_qty_req from me_mtr_use_h mmuh 
			left join me_mtr_use_b mmub on mmub.me_mtr_use_h_id=mmuh.me_mtr_use_h_id
			where mmuh.me_mtr_use_h_no=_mtr_use_no
			and mmuh.me_mtr_use_h_io_type ='0'
			and upper(mmub.part_no)=upper(_part_no);
			
	
			select coalesce (sum(mmub.part_qty),0) into _part_qty_real from me_mtr_use_h mmuh 
			left join me_mtr_use_sn_part mmub on mmub.me_mtr_use_h_id=mmuh.me_mtr_use_h_id
			where mmuh.me_mtr_use_h_no=_mtr_use_no 
			and upper(mmub.part_no)=upper(_part_no);
		
			if  _part_qty_real>= _part_qty_req then
				res := row('false','此品号: '||_part_no||'，已发料完成，不允许再扫码发料');
				return to_json(res);
			end if;
		
			
			select coalesce(sum(mmub.part_qty_real),'0')::numeric into _part_qty_real_2 from me_mtr_use_h mmuh 
			left join me_mtr_use_b mmub on mmub.me_mtr_use_h_id=mmuh.me_mtr_use_h_id
			where mmuh.me_mtr_use_h_no=_mtr_use_no and mmub.part_no=_part_no ;
			
		
			if not exists(select distinct 1 from me_mtr_use_h mmuh 
						left join me_mtr_use_b mmub on mmub.me_mtr_use_h_id=mmuh.me_mtr_use_h_id
						where mmuh.me_mtr_use_h_no=_mtr_use_no and upper(mmub.part_no)=upper(_part_no)) then
				res := row('false','此物料(物料编码: '||_part_no||'  标签:'||_sn_no||')不在领料单('||_mtr_use_no||')需求物料明细内，请核对!');
				return to_json(res);
			end if;
		
	----因2022.11.26 仓库发料扫描卡顿故障，逐段排查先取消先进先出、有效期管控脚本
	/*	
		--报废物料不管控先进先出与有效期，研发(发)、报废(发)这两种单据类型
		if(_me_mtr_use_h_sap_type in('11','19')) then
			
		else
				--仓库发料先进先出控制---------------------
			if coalesce(_supplier_no,'') = '' then --供应商编号变量值为空，则执行以下脚本
			
			select ws.produce_date into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;
		---	
			select ws.sn_no ,ws.produce_date ,case when ws.invp_no is null then ' ' when ws.invp_no ='' then ' ' else ws.invp_no end
			into _yx_sn_no,_min_produce_date,_min_sn_invp_no
			from wm_sn ws 
			where ws.part_no =_part_no
			and ws.part_qty > 0
			and not exists (select 1 from me_mtr_use_sn_part t where t.sn_no = _sn_no  )--变量_sn_no
			and exists (select 1 from wm_invp wi where wi.invp_no = ws.invp_no)  --不要关联库位
			and not exists (select 1 from szjy_wm_sn_issue_chaofa cf where cf.sn_no = _sn_no)
			and ws.sn_status = '110'
			order by ws.produce_date limit 1;
		
		
			if exists (select 1 from pd_part pp where pp.is_fifo =true and pp.part_no =_part_no) and date(_send_produce_date)>date(_min_produce_date) 
				then 
				 
				res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';生产日期:'||date(_min_produce_date)||';当前库位:'||_min_sn_invp_no);
				return to_json(res);
				
			end if;
		
			else --则执行用供应商编号查询条码
			
			select ws.produce_date into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;
			
			select ws.sn_no ,ws.produce_date ,case when ws.invp_no is null then ' ' when ws.invp_no ='' then ' ' else ws.invp_no end
			into _yx_sn_no,_min_produce_date,_min_sn_invp_no
			from wm_sn ws 
			where ws.part_no =_part_no
			and ws.supplier_no = lower(_supplier_no)
			and ws.part_qty > 0
			and not exists (select 1 from me_mtr_use_sn_part t where t.sn_no = _sn_no  )
			and exists (select 1 from wm_invp wi where wi.invp_no = ws.invp_no)
			and not exists (select 1 from szjy_wm_sn_issue_chaofa cf where cf.sn_no = _sn_no)
			and ws.sn_status = '110'
			order by ws.produce_date limit 1;
		
		
			if exists (select 1 from pd_part pp where pp.is_fifo =true and pp.part_no =_part_no) and date(_send_produce_date)>date(_min_produce_date) 
				then 
				 
				res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';生产日期:'||date(_min_produce_date)||';当前库位:'||_min_sn_invp_no);
				return to_json(res);
				
			end if;
		
			end if;  --判断结束
			
		end if;
			
	*/
		
			--报废物料不管控先进先出与有效期，研发(发)、报废(发)这两种单据类型
		if(_me_mtr_use_h_sap_type in('11','19')) then
			
		else
			----有效期管控-------------------------------
			if exists (select 1 from pd_part pp where pp.part_no =_part_no and pp.is_valid_date_manage=true  ) then 
				if exists (select 1 from wm_sn ws where ws.sn_no =_sn_no and coalesce (ws.valid_date_to,date(ws.produce_date) + 365) <date(now()) ) then 
					res := row('false','品号:'||_part_no||',已启用有效期管控！该物料批次已过有效期，不允许生产发料！');
					return to_json(res);
				end if;
			end if;
		
			/*--判断物料是否开启先进先出管控
			if exists(select 1 from pd_part pp where pp.is_fifo =true and pp.part_no =_part_no)then 
					--仓库发料先进先出控制---------------------
				if coalesce(_supplier_no,'') = '' then --供应商编号变量值为空，则执行以下脚本
				
					select ws.produce_date into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;
				---	
					select ws.sn_no ,ws.produce_date ,case when ws.invp_no is null then ' ' when ws.invp_no ='' then ' ' else ws.invp_no end
					into _yx_sn_no,_min_produce_date,_min_sn_invp_no
					from wm_sn ws 
					where ws.part_no =_part_no
					and ws.part_qty > 0
					and not exists (select 1 from me_mtr_use_sn_part t where t.sn_no = _sn_no  )--变量_sn_no
					and exists (select 1 from wm_invp wi where wi.invp_no = ws.invp_no)  --不要关联库位
					and not exists (select 1 from szjy_wm_sn_issue_chaofa cf where cf.sn_no = _sn_no)
					and ws.sn_status = '110'
					order by ws.produce_date limit 1;
				
				
					if (date(_send_produce_date)>date(_min_produce_date)) then 
						 
						res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';生产日期:'||date(_min_produce_date)||';当前库位:'||_min_sn_invp_no);
						return to_json(res);
						
					end if;
			
				else --则执行用供应商编号查询条码
				
					select ws.produce_date into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;
					
					select ws.sn_no ,ws.produce_date ,case when ws.invp_no is null then ' ' when ws.invp_no ='' then ' ' else ws.invp_no end
					into _yx_sn_no,_min_produce_date,_min_sn_invp_no
					from wm_sn ws 
					where ws.part_no =_part_no
					and ws.supplier_no = lower(_supplier_no)
					and ws.part_qty > 0
					and not exists (select 1 from me_mtr_use_sn_part t where t.sn_no = _sn_no  )
					and exists (select 1 from wm_invp wi where wi.invp_no = ws.invp_no)
					and not exists (select 1 from szjy_wm_sn_issue_chaofa cf where cf.sn_no = _sn_no)
					and ws.sn_status = '110'
					order by ws.produce_date limit 1;
				
				
					if  (date(_send_produce_date)>date(_min_produce_date))then 
						 
						res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';生产日期:'||date(_min_produce_date)||';当前库位:'||_min_sn_invp_no);
						return to_json(res);
						
					end if;
			
				end if;  --判断结束	
			end if;*/
			
		end if;
		
			
		
		
		
		
		------------------------------------------

		
		---------------------------------------
			/*校验物料批次号
			 if coalesce(_part_lot_no,'')<>'' then
				if exists(select distinct 1 from wm_sn where part_no=_part_no and invp_no=_invp_no and lot_no<_part_lot_no and part_qty>0) then 
					_err_msg := format('此物料【物料编码:%s】存在更早批次号物料', _part_no);
					res:=row('false', _err_msg);
					return to_json(res);
				end if;
			end if;*/
		
			if (_part_qty_tm <= 0) or (_part_qty_send<=0) then
				res := row('false','标签数量(或者发料数量)小于或等于 0，不能发料.');
				return to_json(res);
			end if;
		
			if _part_qty_tm < _part_qty_send then
				res := row('false','此物料(物料编码: '||_part_no||'  标签:'||_sn_no||')数量小于发料数量!');
				return to_json(res);
			end if;
		--条码有发料记录并且超发数量=0或者没有超发记录则不能发料
			select part_qty into _part_qty_cf from szjy_wm_sn_issue_chaofa f where f.sn_no = _sn_no;
			select count(*) into _part_qty_count from me_mtr_use_sn_part p
			left join me_mtr_use_h h on h.me_mtr_use_h_id = p.me_mtr_use_h_id 
			where h.me_mtr_use_h_io_type = '0' and  p.sn_no = _sn_no;
		if (_part_qty_count > 0) then
			if (select 1 from szjy_wm_sn_issue_chaofa f where f.sn_no = _sn_no) then 
				if (_part_qty_cf <= 0) then 
					if not exists(select 1 from wm_io where  sn_no = _sn_no and wm_io_type_name = '超发归还') then
						res := row('false',concat('条码:',_sn_no,'已发料并且没有超发数量，不能再次发料！！！'));
						return to_json(res);
					end if;
				end if;
			else 
				res := row('false',concat('条码:',_sn_no,'已发料并且没有超发数量，不能再次发料！！！'));
				return to_json(res);
			end if;
			
			
		end if;
		
		 --超发扣账必须是同车间或者同部门，否则不能执行扣账
		if exists(select 1 from szjy_wm_sn_issue_chaofa sw where sw.sn_no = _sn_no) then
			if not exists(select 1 from wm_io where  sn_no = _sn_no and wm_io_type_name = '超发归还') then
				if not exists(select 1 from szjy_wm_sn_issue_chaofa sw where sw.sn_no = _sn_no and upper(sw.workshop_no) = upper(_workshop_no) and sw.part_qty > 0) then
					res := row('false',concat('条码:',_sn_no,'已超发，',_workshop_no,'不是同车间或者同部门，不能超发扣账！！！'));
					return to_json(res);
				end if;
			end if;
		end if;
	
	--扫描发料自动解绑定，物料和栈板条码解绑定
		select m.sn_pack_40 into _sn_pack_40 from wm_sn m where m.sn_no = _sn_no;
		if (_sn_pack_40 != null or _sn_pack_40 != '') then
			update wm_sn set sn_pack_40 = '' where sn_no = _sn_no;
		end if;
		
		  --判断条码是否在超发台账库中，如果不存在则拆分条码，存在自动扣减数量
	--	if not exists (select 1 from szjy_wm_sn_issue_chaofa swsic where swsic.sn_no =_sn_no ) then 
			--1.不允许拆分
			if _part_qty_send+_part_qty_real > _part_qty_req and _part_qty_send < _part_qty_tm then
				res := row('false',concat('发料数量：',_part_qty_send,'大于需求数量：',_part_qty_req,'并且小于条码数量：',_part_qty_tm,'请使用产品条码拆分或者条码数量全部超发！！！'));
				return to_json(res);
			end if;
			--2.允许拆分
			if _part_qty_send+_part_qty_real <= _part_qty_req and _part_qty_send < _part_qty_tm then
				--校验条码是否超发，如果超发则不能进行拆分,2023-07-11增加
				if exists(select 1 from szjy_wm_sn_issue_chaofa sw where sw.sn_no = _sn_no)then
					res := row('false','条码：'||_sn_no||' 已超发不能拆分发料！！！');
					return to_json(res);
				end if;
					--定义条码拆分规则是拆出来的新条码号为原条码后加‘-拆分次数’，eg: 原条码= 12345678  拆出来的新条码=12345678-01, 12345678-02... 
				if not exists(select distinct 1 from wm_sn where (sn_no = _sn_no||'-01') and length(sn_no)=(length(_sn_no)+3)) then
					_new_sn_no := _sn_no||'-01';
				else
					select max(sn_no) into _part_sn_no_tmp from wm_sn where sn_no like _sn_no||'%' and length(sn_no)=length(_sn_no)+3;
					_new_sn_no := _sn_no||'-'||lpad((substring(_part_sn_no_tmp, '..$')::int+1)::text, 2, '0');
				end if;
					 --_new_sn_no := af_ss_no_generate('sn_no');
				
--				--根据发料数量拆分生成新标签
				INSERT INTO public.wm_sn
					(sn_no, sn_type, sn_type_name, part_no, part_name, part_spec, part_idt, part_qty, lot_no, supplier_id, supplier_no, 
				 	supplier_name, valid_date_to, sn_status, sn_status_name, qa_type, mo_no, unode, wkn, wkp_no, wkp_name, ea_id, ea_no, 
				 	ea_name, invp_id, invp_no, sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, factory_no,  pack_rule_name, 
				 	pack_qty_max, pack_qty_used, pack_is_closed, print_times, weight_gross, weight_net, length, width, height, fb_id, 
				 	sr_dlv_h_id, wm_sn_rmk01, wm_sn_rmk02, wm_sn_rmk03, wm_sn_rmk04, wm_sn_rmk05, wm_sn_rmk06, 
				 	crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
				 	upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
				 	sn_orgin_type, sn_orgin_type_name, is_qa_ng, is_hold_to_use,po_h_no,produce_date,year_week) --year_week 2025.3.12添加
				 	
				 	select _new_sn_no,sn_type, sn_type_name,part_no, part_name, part_spec, part_idt, _part_qty_send,lot_no, supplier_id, supplier_no, 
				 	supplier_name,valid_date_to, sn_status, sn_status_name,qa_type, mo_no, unode, wkn, wkp_no, wkp_name, ea_id, ea_no,
				 	ea_name, invp_id, invp_no,sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50,factory_no,pack_rule_name,
				 	pack_qty_max, pack_qty_used, pack_is_closed, 0, 0, 0, 0, 0, 0,fb_id,
				 	sr_dlv_h_id,wm_sn_rmk01, wm_sn_rmk02, wm_sn_rmk03, wm_sn_rmk04, wm_sn_rmk05, wm_sn_rmk06, 
				 	localtimestamp, _user_id, _user_no, _user_name, 'pda',
				 	localtimestamp, _user_id, _user_no, _user_name, 'pda',
				 	sn_orgin_type, sn_orgin_type_name,is_qa_ng, is_hold_to_use,po_h_no,produce_date,year_week --year_week 2025.3.12添加
					from wm_sn
					where sn_no=_sn_no;
				
--				--写入拆分记录
--	
				INSERT INTO public.wm_io
					(wm_io_id, wm_io_datetime, part_no, part_name, part_spec, part_unit, part_idt, lot_no, 
					mrp_region_no, invp_qa_type, invp_no, invp_qty_before, wm_io_qty, invp_qty_after, wm_io_type, 
					wm_io_type_name, wm_io_reason, wm_io_reason_name, wm_io_dept_no, wm_io_dept_name, 
					wm_io_worker, wm_io_worker_no, wm_io_worker_name, order_type, order_type_name, order_id, 
					order_no, wm_qty_rmk01, wm_qty_rmk02, wm_qty_rmk03, wm_qty_rmk04, wm_qty_rmk05, wm_qty_rmk06, 
					wm_qty_rmk07, wm_qty_rmk08, wm_qty_rmk09, wm_qty_rmk10, 
					crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
					upd_time, upd_user, upd_user_no, upd_user_name, upd_host, sn_no)
					select 
					af_auid(), now(), a.part_no , a.part_name , a.part_spec ,pp.part_unit  , '', a.lot_no , 
					'', 'ok', coalesce (a.invp_no,'') , a.part_qty , a.part_qty, a.part_qty, '5', 
					'拆分', '', '', '', '', 
					'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 
					now(), _user_id, _user_no, _user_name, 'PDA', 
					now(), _user_id, _user_no, _user_name, 'PDA',  
					a.sn_no
					from wm_sn a
					left join pd_part pp on pp.part_no =a.part_no 
					where sn_no=_new_sn_no ;
			
				end if;
		
			/*if exists(select distinct 1 from wm_sn where part_no=_part_no and coalesce(lot_no,'')=coalesce(_part_lot_no,'')
						and coalesce(wm_sn_rmk06,'')=coalesce(_mrp_no_part,'') and coalesce(invp_no,'')=coalesce(_invp_no,'') 
						and part_qty>0 and upd_time<_prod_stockin_dt) then
				res := row('false','违反物料先进先出原则.');
				return to_json(res);
			end if;*/
		
			select mh.me_mtr_use_h_id into _mtr_use_h_id
			from me_mtr_use_h mh
		    where me_mtr_use_h_no = _mtr_use_no;
		
			--先注释XZP
			/*
		
			if exists(select 1 from me_mtr_use_h mmuh 
						left join me_mtr_use_b mmub on mmub.me_mtr_use_h_id=mmuh.me_mtr_use_h_id
						where mmuh.me_mtr_use_h_no=_mtr_use_no and mmub.part_no=_part_no and mmub.part_qty_real=0
							and mmub.mrp_region_no=coalesce(_mrp_no_part,'')) then
				update public.me_mtr_use_b set part_qty_real=_part_qty_send,mrp_region_no=_mrp_no_part,me_mtr_use_b_rmk1=_part_lot_no,lot_no=_mo_no,me_mtr_use_b_rmk5=_mrp_no_part,me_mtr_use_b_rmk6=_unode,
						upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
				where me_mtr_use_h_id=_mtr_use_h_id and part_no=_part_no and coalesce(mrp_region_no,'')=coalesce(_mrp_no_part,'');
			else
				if exists(select 1 from me_mtr_use_h mmuh 
						left join me_mtr_use_b mmub on mmub.me_mtr_use_h_id=mmuh.me_mtr_use_h_id
						where mmuh.me_mtr_use_h_no=_mtr_use_no and mmub.part_no=_part_no and coalesce(me_mtr_use_b_rmk1,'')=coalesce(_part_lot_no,'')
							and mmub.mrp_region_no=coalesce(_mrp_no_part,'')) then

					update public.me_mtr_use_b set part_qty_real=part_qty_real+_part_qty_send,mrp_region_no=_mrp_no_part,me_mtr_use_b_rmk1=_part_lot_no,lot_no=_mo_no,me_mtr_use_b_rmk5=_mrp_no_part,me_mtr_use_b_rmk6=_unode,
						upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
					where me_mtr_use_h_id=_mtr_use_h_id and part_no=_part_no and coalesce(me_mtr_use_b_rmk1,'')=coalesce(_part_lot_no,'') and coalesce(mrp_region_no,'')=coalesce(_mrp_no_part,'');
				else
					insert into public.me_mtr_use_b
					(me_mtr_use_b_id, me_mtr_use_h_id, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty_req, part_qty_real, me_mtr_use_b_rmk1, me_mtr_use_b_rmk2, me_mtr_use_b_rmk3, me_mtr_use_b_rmk4, me_mtr_use_b_rmk5, me_mtr_use_b_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, io_is_sucessed, io_times, io_last_time)
					select af_auid(), _mtr_use_h_id, tmp.part_no,tmp.part_name, tmp.part_spec, pp.part_unit, awp.wms_part_unit_name, tmp.part_idt, _mrp_no_part, _mo_no, tmp.invp_no, wi.invp_name, _part_qty_req, _part_qty_send, _part_lot_no, null, null, null, tmp.wm_sn_rmk06, _unode, localtimestamp, _user_id, _user_no, _user_name, 'pda', localtimestamp, _user_id, _user_no, _user_name, 'pda', false, 0, localtimestamp
					from wm_sn tmp
					left join pd_part pp on pp.part_no=tmp.part_no
					left join av_wms_part_unit awp on awp.wms_part_unit=pp.part_unit 
					left join wm_invp wi on wi.invp_no=tmp.invp_no
					where tmp.sn_no=_sn_no;
				end if;
			end if;
		*/
		   
		 
		
			---退料时使用
	
			if _part_qty_tm > _part_qty_send then
			
				insert into public.me_mtr_use_sn_part
					(me_mtr_use_sn_part_id, me_mtr_use_h_id, feeder_no, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name,supplier_no,supplier_name, part_qty, me_mtr_use_sn_part_rmk1, me_mtr_use_sn_part_rmk2, me_mtr_use_sn_part_rmk3, me_mtr_use_sn_part_rmk4, me_mtr_use_sn_part_rmk5, me_mtr_use_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, io_is_sucessed, io_times, io_last_time)
				select af_auid(), _mtr_use_h_id, '', _new_sn_no, ws.part_no, ws.part_name, ws.part_spec, awp.qty_unit_no , awp.qty_unit_name , ws.part_idt, '', ws.lot_no, coalesce (ws.invp_no,''), '',ws.supplier_no,ws.supplier_name, _part_qty_send, '', null, null, null, null, null, localtimestamp, _user_id, _user_no, _user_name, 'pda', localtimestamp, _user_id, _user_no, _user_name, 'pda', false, 0, localtimestamp
				from wm_sn ws
				left join pd_part pp on pp.part_no=ws.part_no
				left join av_ss_qty_unit  awp on awp.qty_unit_no =pp.part_unit
				where sn_no =_sn_no;
				
						
				update public.wm_sn set part_qty=part_qty-_part_qty_send,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
				where sn_no=_sn_no;
			
				update public.wm_sn set part_qty=_part_qty_send,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
				where sn_no=_new_sn_no;
			
				if exists (select 1 from szjy_wm_sn_issue_chaofa swsic where swsic.sn_no =_sn_no  ) then 
					
						--更新超发台账表
						update szjy_wm_sn_issue_chaofa 
						set part_qty =part_qty - _part_qty_send ,
						   ea_no = _ea_no,
						   ea_name = _ea_name ,
						   workshop_no = _workshop_no,
						   wm_io_datetime= now(),
						   rmk01 =_mtr_use_no,
						   upd_user =_user_id,
						   upd_user_no =_user_no ,
						   upd_user_name =_user_name   
						where sn_no =_sn_no;
			
				end if;
			
				--打印拆条码
				if( _new_sn_no != '' or _new_sn_no notnull) then
					select array_agg(row_to_json(tmp)) into tmpjson 
					from (select 
						'jinyang_tm1' as lanya_print_mb_lx,
						part_no as part_no,
						coalesce (part_name,'') as part_name,
						sn_no as sn_no ,
						lot_no as pch,
						part_qty as qty,
						date(produce_date) as sc_rq,
						date(valid_date_to) as baozhang_rq,
						concat(substring(cast(date_part('year',date(produce_date)) as text),3,2),lpad(cast(case when EXTRACT(DOW FROM produce_date)=0 then
						date_part('week',date(produce_date)+1) else date_part('week',date(produce_date)) end as text),2,'0')) as zhouqi,
						weight_net as jz,
						weight_gross as mz,
						supplier_no as gys_bh,
						po_h_no as cg_dh
						from wm_sn where sn_no =_new_sn_no
						UNION all
						select 
						'jinyang_tm1' as lanya_print_mb_lx,
						part_no as part_no,
						coalesce (part_name,'') as part_name,
						sn_no as sn_no ,
						lot_no as pch,
						part_qty as qty,
						date(produce_date) as sc_rq,
						date(valid_date_to) as baozhang_rq,
						concat(substring(cast(date_part('year',date(produce_date)) as text),3,2),lpad(cast(case when EXTRACT(DOW FROM produce_date)=0 then
						date_part('week',date(produce_date)+1) else date_part('week',date(produce_date)) end as text),2,'0')) as zhouqi,
						weight_net as jz,
						weight_gross as mz,
						supplier_no as gys_bh,
						po_h_no as cg_dh
						from wm_sn where sn_no =_sn_no
						) tmp;
					return json_build_object('successful',true,'msg','查询成功','datas',tmpjson);
				end if;
				
			else
			
				insert into public.me_mtr_use_sn_part
					(me_mtr_use_sn_part_id, me_mtr_use_h_id, feeder_no, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name,supplier_no,supplier_name, part_qty, me_mtr_use_sn_part_rmk1, me_mtr_use_sn_part_rmk2, me_mtr_use_sn_part_rmk3, me_mtr_use_sn_part_rmk4, me_mtr_use_sn_part_rmk5, me_mtr_use_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, io_is_sucessed, io_times, io_last_time)
				select af_auid(), _mtr_use_h_id, '', _sn_no, upper(ws.part_no) as part_no, ws.part_name, ws.part_spec, awp.qty_unit_no , awp.qty_unit_name , ws.part_idt, _mrp_no_part, ws.lot_no, ws.invp_no, '',ws.supplier_no,ws.supplier_name, _part_qty_send, '', null, null, null, null, null, localtimestamp, _user_id, _user_no, _user_name, 'pda', localtimestamp, _user_id, _user_no, _user_name, 'pda', false, 0, localtimestamp
				from wm_sn ws
				left join pd_part pp on pp.part_no=upper(ws.part_no)
				left join av_ss_qty_unit  awp on awp.qty_unit_no =pp.part_unit
				where sn_no =_sn_no;
				
			
			----已有超发记录，在超发N次直接更新记录数量
				if exists (select 1 from szjy_wm_sn_issue_chaofa swsic where swsic.sn_no =_sn_no  ) then 
					if (_part_qty_real+_part_qty_send > _part_qty_req) then
						_part_qty_w := _part_qty_req-_part_qty_real; -- 需求数-累计数
						_part_qty_sf := _part_qty_send - _part_qty_w; -- 发料数 - 计算后的数 = 超发数
						update szjy_wm_sn_issue_chaofa 
						set part_qty = _part_qty_sf,
						   ea_no = _ea_no,
						   ea_name = _ea_name ,
						   workshop_no = _workshop_no,
						   wm_io_datetime= now(),
						   rmk01 =_mtr_use_no,
						   upd_user =_user_id,
						   upd_user_no =_user_no ,
						   upd_user_name =_user_name   
						where sn_no =_sn_no;
					end if;
				end if;
					   
		     ---写入超发台账	
					
				if (_part_qty_real+_part_qty_send)>_part_qty_req then
					_part_qty_w := _part_qty_req-_part_qty_real;
					_part_qty_sf := _part_qty_send - _part_qty_w;
	
					insert into szjy_wm_sn_issue_chaofa (
							szjy_wm_sn_issue_chaofa_id,sn_no ,	ea_no ,	ea_name ,	workshop_no ,	wm_io_datetime,
							part_no ,part_name ,part_spec ,	part_qty,part_unit,invp_worker_no,invp_worker_name,
							rmk01,rmk02,rmk03,rmk04,rmk05,supplier_no,sr_dlv_h_id,produce_date,
							crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
							upd_time,upd_user,upd_user_no,upd_user_name,upd_host)
					select af_auid(),ws.sn_no ,_ea_no,_ea_name , _workshop_no,now(),
							ws.part_no ,ws.part_name ,ws.part_spec ,_part_qty_sf,pp.part_unit ,pp.part_type02 ,mw.worker_name ,
							_mtr_use_no,'','','','',ws.supplier_no,ws.sr_dlv_h_id,ws.produce_date,
							now(),_user_id, _user_no, _user_name, 'pda',
							now(),_user_id, _user_no, _user_name, 'pda'
							from wm_sn ws 
							left join pd_part pp on pp.part_no = ws.part_no 
							left join me_worker mw on mw.worker_no = pp.part_type02 
							where ws.sn_no = _sn_no
							and not exists (select sn_no from szjy_wm_sn_issue_chaofa c where c.sn_no=ws.sn_no  )
							;
					
				else 
				
					--更新超发台账表
					update szjy_wm_sn_issue_chaofa 
					set part_qty =part_qty - _part_qty_send ,
					   ea_no = _ea_no,
					   ea_name = _ea_name ,
					   workshop_no = _workshop_no,
					   wm_io_datetime= now(),
					   rmk01 =_mtr_use_no,
					   upd_user =_user_id,
					   upd_user_no =_user_no ,
					   upd_user_name =_user_name   
					where sn_no =_sn_no;
				
						
									
				end if;
			
			
				update public.wm_sn w set part_qty=w.part_qty-_part_qty_send + coalesce (f.part_qty,0) ,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
				from szjy_wm_sn_issue_chaofa f 
				where w.sn_no = f.sn_no 
			    and w.sn_no=_sn_no;
				
			end if;

		
			
		end loop;
		----移到af_pda_wms_314a  20220113-end-----------------
		select array_agg(row_to_json(tmp)) into tmpjson 
				from (
					select 
					'jinyang_tm1' as lanya_print_mb_lx,
					true as not_print,
					part_no as part_no,
					coalesce (part_name,'') as part_name,
					sn_no as sn_no ,
					lot_no as pch,
					part_qty as qty,
					date(produce_date) as sc_rq,
					date(valid_date_to) as baozhang_rq,
					weight_net as jz,
					weight_gross as mz,
					supplier_no as gys_bh,
					po_h_no as cg_dh
					from wm_sn where sn_no =_sn_no
					) tmp;
		return json_build_object('successful',true,'msg','发料完成','datas',tmpjson);
		/*res := row('true','仓库发料完成!');
		return to_json(res);*/
	
	exception when others then
		GET STACKED diagnostics
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);	
	
	END;
$function$
;

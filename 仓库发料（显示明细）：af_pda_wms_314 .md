
```sql
-- DROP FUNCTION public.af_ax_wms_sap_submit_info(varchar, varchar);

CREATE OR REPLACE FUNCTION public.af_ax_wms_sap_submit_info(upid character varying, method_name character varying)
 RETURNS TABLE(finish_io_no text, datainfo json)
 LANGUAGE plpgsql
AS $function$

declare 
     
    _json_all json;
    result   returntype;

begin

--   insert into log_test(value,type) 
--   select me_finish_io_no,'af_ax_wms_sap_submit_info' from temp_tb;
  
--入库申请
   if method_name='mes-sap-Addon' then 
      
	   RETURN QUERY
	   select h.me_finish_io_no,
		json_build_object('U_Type',h.mo_type,'U_prccode',h.workshop_no,'U_prcname',h.me_finish_io_rmk1,'U_Status','N','U_DueDate',to_char(current_date,'yyyy-mm-dd')
		,'U_DocDate',to_char(h.finish_io_datetime,'yyyy-mm-dd'),'U_Note1','','U_Crman','Mes','FQCTEMP1',
		    case when strpos(h.mo_no,'F')=1 then json_agg(json_build_object('U_BaseType','分切单','U_SpNum'
		    ,REPLACE(h.mo_no,'F',''),'U_SpLine',1,'U_ItemCode',h.part_no,'U_Quantity',h.finish_io_qty_ok,'U_OkQTY',h.finish_io_qty_ok,'U_Batchnum',h.lot_no)) 
		    else json_agg(json_build_object('U_OWdocNum',h.mo_no,'U_ItemCode',h.part_no,'U_Quantity',h.finish_io_qty_ok
		    ,'U_OkQTY',h.finish_io_qty_ok,'U_Batchnum',h.lot_no,'U_BaseType','生产工单')) end ) as dataInfo 
		from me_finish_io_h h 
		left join qm_si_lot_h qm on qm.move_order_no = h.me_finish_io_no
		where  coalesce(h.sap_inbound_apply_no,'')='' and qm.si_conclusion_name='合格'  and h.crt_time>='2025.7.4'  
	    and  h.me_finish_io_no=upid
		group by me_finish_io_no;

   end if;
  
  --入库
  if method_name='mes-sap-OIGN' then 
       --将查询语句转换 
	   RETURN QUERY
	   select ma.me_finish_io_no,
		case when strpos(ma.mo_no,'F')=0 then
		json_build_object('DocDate',to_char(current_date,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
		json_agg(json_build_object('BaseType',202,'BaseRef',ma.mo_no,'WhsCode',mc.invp_area_no,'QuanTity',finish_io_qty_ok,'BatchNo',ma.lot_no,'AcctCode',
		mb.acct_code,'U_BaseType','FQCTEMP','U_QtDocNum',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),'U_QtLine','1')))
		else json_build_object('DocDate',to_char(current_date,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
		json_agg(json_build_object('ItemCode',ma.part_no,'WhsCode',mc.invp_area_no,'QuanTity',ma.finish_io_qty_ok,'BatchNo',ma.lot_no,'AcctCode',
		mb.acct_code,'Price',mb.price,'U_BaseType','SPLIT1','U_basedoc',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),
		'U_baseline','1','U_OPLANNUM',replace(ma.mo_no,'F',''),'U_OPLNUM','1','U_QtDocNum',replace(ma.mo_no,'F',''),'U_QtLine','1'))) end  as datas
		from me_finish_io_h ma
		left join wm_temp_receipt re on ma.me_finish_io_no=re.delivery_no
		left join qm_si_lot_h c on c.move_order_no=re.temp_receipt_no
		left join mo mb on mb.mo_no=ma.mo_no 
		left join pd_part mc on mc.part_no=ma.part_no
		 where  coalesce(ma.sap_inbound_apply_no,'')!='' and coalesce(ma.sap_inbound_no,'')='' and ma.me_finish_io_no=upid
		 and (coalesce(ma.me_finish_io_rmk4,'')='入库完成' or (coalesce(c.si_conclusion_no,'')<>'' and (ma.workshop_no='J129' or ma.workshop_no='J171' )) )
		 and  ma.crt_time>='2025.7.4' 
		group by ma.me_finish_io_no ;

   end if;

  --销售退货单
  if method_name='mes-sap-ORDN' then 
        --将查询语句转换  正式库：410  测试库：379   RetCost：退货成本
	    RETURN QUERY
        select 
		sa.cr_rtn_h_no,
		json_build_object(
		'CardCode',sa.client_no,
		'DocDate',to_char(sa.upd_time, 'yyyy-mm-dd'),
		'OwnerCode',379,
		'U_WebNo',sa.cr_rtn_h_no,
		'details',
		json_agg(json_build_object('ItemCode',sb.part_no,'QuanTity',sb.part_Qty_real,'BatchNo','MES888888','WhsCode',sb.invp_area,
		'PriceAfterVAT',sb.price_afvat,'Currency',sb.currency,'EnSetCost',1,'RetCost',sb.stock_price,'U_BaseType','销售交货',
		'U_QtDocNum',sb.so_h_no,'U_QtLine',split_part(sb.so_b_id,'_',2),'U_KHPO',sb.u_khpo
        ,'U_JCDOC',crb.so_h_no,'U_JCLINE',split_part(crb.so_b_id,'_',2)
        ,'U_Reqnum',crb.so_h_no,'U_Reqline',split_part(crb.so_b_id,'_',2)
        )) 
		) as datas
		from szjy_mes_cr_rtn_h sa
		left join szjy_mes_cr_rtn_b sb on sb.cr_rtn_h_id=sa.cr_rtn_h_id
		left join szjy_sap_cr_dlv_b crb on sb.so_b_id=crb.cr_dlv_b_id
		where sa.cr_rtn_rmk06 ='仓库扫描收货完成' and sa.cr_rtn_h_no=upid
        and sa.cr_rtn_datetime>='2025.8.19'
		and (sa.sap_bill_no='' or sa.sap_bill_no is null)
		group by sa.cr_rtn_h_no,sa.client_no,sa.upd_time;


   end if;
  
end;

$function$
;


### 功能逻辑说明

1.当前功能是一个仓库发料功能

2.执行流程：校验单据、校验条码、执行发料

af_query_wms_314 》af_query_wms_401》af_pda_wms_314


我来帮您分析仓库发料业务流程和数据表中的数据流程。首先让我查看这几个文件的内容。

现在让我查看一下是否还有其他相关的文件来完善分析：

基于这四个文件的内容，我来为您详细分析仓库发料业务流程和数据表中的数据流程：

## 仓库发料业务流程分析

### 1. 整体业务流程概述

根据功能说明，仓库发料功能的执行流程为：
```
校验单据 → 校验条码 → 执行发料
af_query_wms_314 → af_query_wms_401 → af_ax_wms_sap_submit_info
```

### 2. 详细业务流程

#### 第一步：领料单号校验（af_query_wms_314）

**功能目标**：根据领料单号查询明细，返回物料信息

**主要校验逻辑**：
1. **取消状态检查**：检查领料单是否已被SAP取消（`me_mtr_use_h_rmk7 = 'Y'`）
2. **物料需求统计**：汇总领料单中各物料的需求数量
3. **实际发料统计**：统计已发料数量（排除委外五标签条码）
4. **差异计算**：计算实际发料与需求的差异

**涉及数据表**：
- `me_mtr_use_h`：领料单头表
- `me_mtr_use_b`：领料单明细表  
- `me_mtr_use_sn_part`：领料单条码发料记录表

#### 第二步：条码校验（af_query_wms_401）

**功能目标**：根据物料条码SN_NO查询并校验条码有效性

**主要校验逻辑**：

1. **基础信息获取**：从`wm_sn`表获取条码的基本信息
2. **检验状态校验**：
   - 期初条码（Q）、其它入库条码（QT）、退料条码（T）跳过检验
   - 其他条码需检查品保检验状态（合格/特采）
3. **库存状态校验**：
   - 检查条码是否已上架（`invp_no`不为空则需先下架）
   - 检查条码是否冻结（`is_hold_to_use = true`）
   - 检查条码状态是否作废（`sn_status = '130'`）
4. **先进先出管控**：
   - 检查物料是否启用FIFO管控（`pd_part.is_fifo = true`）
   - 按生产日期周期计算，确保先进先出
   - 超发扣账物料不检查先进先出
5. **供应商匹配**：如有供应商编号，需验证条码供应商是否匹配

**涉及数据表**：
- `wm_sn`：条码主表
- `qm_si_lot_h`：检验单表
- `pd_part`：物料主表
- `me_mtr_use_h`：领料单头表
- `me_mtr_use_sn_part`：发料记录表
- `szjy_wm_sn_issue_chaofa`：超发扣账表
- `wm_invp`：库位表

#### 第三步：执行发料（af_ax_wms_sap_submit_info）

**功能目标**：处理不同类型的SAP单据提交

**主要处理类型**：
1. **入库申请（mes-sap-Addon）**：处理完工入库申请
2. **入库单（mes-sap-OIGN）**：处理实际入库操作
3. **销售退货单（mes-sap-ORDN）**：处理销售退货

**涉及数据表**：
- `me_finish_io_h`：完工入库头表
- `qm_si_lot_h`：检验单表
- `wm_temp_receipt`：临时收货表
- `mo`：工单表
- `pd_part`：物料主表
- `szjy_mes_cr_rtn_h/b`：销售退货表
- `szjy_sap_cr_dlv_b`：SAP交货表

## 数据流程分析

### 1. 数据流向图

```mermaid
graph TD
    A[PDA扫描领料单号] --> B[af_query_wms_314]
    B --> C{领料单状态检查}
    C -->|正常| D[查询物料需求明细]
    C -->|已取消| E[返回错误信息]
    D --> F[PDA扫描物料条码]
    F --> G[af_query_wms_401]
    G --> H{条码校验}
    H -->|通过| I[返回物料信息]
    H -->|失败| J[返回错误信息]
    I --> K[确认发料]
    K --> L[af_ax_wms_sap_submit_info]
    L --> M[更新发料记录]
    M --> N[同步SAP系统]
```

### 2. 关键数据表关系

```mermaid
erDiagram
    me_mtr_use_h ||--o{ me_mtr_use_b : "1对多"
    me_mtr_use_h ||--o{ me_mtr_use_sn_part : "1对多"
    wm_sn ||--o{ me_mtr_use_sn_part : "1对多"
    pd_part ||--o{ wm_sn : "1对多"
    pd_part ||--o{ me_mtr_use_b : "1对多"
    qm_si_lot_h ||--o{ wm_sn : "检验关联"
    wm_invp ||--o{ wm_sn : "库位关联"
    
    me_mtr_use_h {
        string me_mtr_use_h_id "领料单ID"
        string me_mtr_use_h_no "领料单号"
        string me_mtr_use_h_rmk7 "取消标识"
        string me_mtr_use_h_sap_type "SAP类型"
    }
    
    wm_sn {
        string sn_no "条码号"
        string part_no "物料编号"
        numeric part_qty "数量"
        string sn_status "条码状态"
        boolean is_hold_to_use "冻结标识"
        string invp_no "库位编号"
        date produce_date "生产日期"
    }
```

### 3. 业务规则总结

1. **领料单管控**：
   - 已取消的领料单不能发料
   - 需统计已发料数量与需求数量的差异

2. **条码管控**：
   - 已上架条码需先下架
   - 冻结条码禁止发料（报废单除外）
   - 作废条码禁止发料
   - 需通过品保检验（特定条码类型除外）

3. **先进先出管控**：
   - 启用FIFO的物料必须按生产日期周期顺序发料
   - 超发扣账物料不受FIFO限制
   - 按供应商分组管理FIFO

4. **数据同步**：
   - 发料完成后需同步SAP系统
   - 支持多种单据类型的处理

这个仓库发料系统通过三层校验确保了发料的准确性和合规性，同时与SAP系统保持数据同步，是一个完整的WMS发料解决方案。


### 功能逻辑说明

1.当前功能是一个仓库发料功能

2.执行流程：校验单据、校验条码、执行发料

af_query_wms_314 》af_query_wms_401》af_pda_wms_314

您说得对，我需要重新分析。让我先查看正确的仓库发料函数 `af_pda_wms_314`：

现在让我查看完整的 `af_pda_wms_314` 函数内容：

现在我已经看到了正确的仓库发料函数。让我重新分析整个仓库发料业务流程：

## 仓库发料业务流程重新分析

### 1. 整体业务流程概述

根据功能说明和实际函数，仓库发料功能的执行流程为：
```
校验单据 → 校验条码 → 执行发料
af_query_wms_314 → af_query_wms_401 → af_pda_wms_314
```

### 2. 详细业务流程分析

#### 第一步：领料单号校验（af_query_wms_314）

**功能目标**：根据领料单号查询明细，返回物料需求信息

**主要业务逻辑**：
1. **取消状态检查**：检查领料单是否已被SAP取消（`me_mtr_use_h_rmk7 = 'Y'`）
2. **物料需求统计**：
   - 汇总领料单中各物料的需求数量（`part_qty_req`）
   - 统计已发料数量（`part_qty_real`），排除委外五标签条码
   - 计算差异（`diff_qty = part_qty_real - part_qty_req`）

**涉及数据表**：
- `me_mtr_use_h`：领料单头表
- `me_mtr_use_b`：领料单明细表  
- `me_mtr_use_sn_part`：领料单条码发料记录表

#### 第二步：条码校验（af_query_wms_401）

**功能目标**：根据物料条码SN_NO进行全面校验

**主要校验逻辑**：
1. **检验状态校验**：非期初/退料条码需检查品保检验状态
2. **库存状态校验**：上架、冻结、作废状态检查
3. **先进先出管控**：按生产日期周期确保FIFO（超发扣账物料除外）
4. **供应商匹配校验**

**涉及数据表**：
- `wm_sn`：条码主表
- `qm_si_lot_h`：检验单表
- `pd_part`：物料主表
- `szjy_wm_sn_issue_chaofa`：超发扣账表

#### 第三步：执行发料（af_pda_wms_314）

**功能目标**：实际执行仓库发料操作

**主要业务逻辑**：

1. **前置校验**：
   - 条码重复发料检查
   - 单据状态检查（状态需为200或210）
   - 物料需求数量vs已发料数量检查
   - 物料是否在领料单明细内检查

2. **有效期管控**：
   - 检查物料是否启用有效期管控
   - 验证条码是否过期

3. **数量校验**：
   - 标签数量vs发料数量检查
   - 超发条码重复发料检查
   - 超发扣账车间匹配检查

4. **条码拆分逻辑**：
   - **不允许拆分**：发料数量+已发料数量 > 需求数量 且 发料数量 < 条码数量
   - **允许拆分**：发料数量+已发料数量 ≤ 需求数量 且 发料数量 < 条码数量
   - 拆分规则：原条码号后加'-01', '-02'等

5. **发料记录写入**：
   - 写入`me_mtr_use_sn_part`发料记录表
   - 更新`wm_sn`条码数量
   - 处理超发台账`szjy_wm_sn_issue_chaofa`

### 3. 数据流程图

```mermaid
graph TD
    A[PDA扫描领料单号] --> B[af_query_wms_314<br/>领料单校验]
    B --> C{领料单状态}
    C -->|正常| D[返回物料需求明细]
    C -->|已取消| E[返回错误信息]
    
    D --> F[PDA扫描物料条码]
    F --> G[af_query_wms_401<br/>条码校验]
    G --> H{条码校验}
    H -->|通过| I[返回物料信息]
    H -->|失败| J[返回错误信息]
    
    I --> K[确认发料数量]
    K --> L[af_pda_wms_314<br/>执行发料]
    L --> M{发料校验}
    M -->|通过| N[条码拆分判断]
    M -->|失败| O[返回错误信息]
    
    N --> P{需要拆分?}
    P -->|是| Q[创建新条码<br/>更新原条码数量]
    P -->|否| R[直接发料]
    
    Q --> S[写入发料记录]
    R --> S
    S --> T[更新超发台账]
    T --> U[返回成功信息]
```

### 4. 关键数据表关系

```mermaid
erDiagram
    me_mtr_use_h ||--o{ me_mtr_use_b : "领料单头明细"
    me_mtr_use_h ||--o{ me_mtr_use_sn_part : "领料单发料记录"
    wm_sn ||--o{ me_mtr_use_sn_part : "条码发料记录"
    wm_sn ||--o{ szjy_wm_sn_issue_chaofa : "条码超发记录"
    pd_part ||--o{ wm_sn : "物料条码"
    
    me_mtr_use_h {
        string me_mtr_use_h_id "领料单ID"
        string me_mtr_use_h_no "领料单号"
        string me_mtr_use_h_rmk7 "取消标识Y/N"
        string me_mtr_use_h_status "单据状态"
        string me_mtr_use_h_sap_type "SAP类型"
    }
    
    me_mtr_use_sn_part {
        string me_mtr_use_sn_part_id "发料记录ID"
        string me_mtr_use_h_id "领料单ID"
        string sn_no "条码号"
        string part_no "物料编号"
        numeric part_qty "发料数量"
    }
    
    wm_sn {
        string sn_no "条码号"
        string part_no "物料编号"
        numeric part_qty "当前数量"
        string sn_status "条码状态"
        boolean is_hold_to_use "冻结标识"
        date produce_date "生产日期"
    }
    
    szjy_wm_sn_issue_chaofa {
        string sn_no "条码号"
        string workshop_no "车间编号"
        numeric part_qty "超发数量"
    }
```

![1755583213344](D:\金洋\模块_PDA_仓库发料（显示明细)



5. 核心业务规则

1. **领料单管控**：
   - 已取消领料单不能发料
   - 单据状态必须为200或210
   - 物料必须在领料单明细内

2. **条码发料管控**：
   - 同一条码在同一领料单不能重复发料
   - 已发料数量不能超过需求数量
   - 条码数量必须大于等于发料数量

3. **条码拆分规则**：
   - 当发料数量小于条码数量时触发拆分
   - 拆分后的新条码号格式：原条码-01, -02...
   - 超发条码不允许拆分

4. **超发管控**：
   - 超发扣账必须在同车间或同部门
   - 超发数量记录在专门台账表中
   - 超发条码再次发料时自动扣减超发数量

这个系统通过三层校验和完善的数据记录，确保了仓库发料的准确性、可追溯性和业务合规性。


```sql
-- DROP FUNCTION public.af_query_wms_401(varchar);

CREATE OR REPLACE FUNCTION public.af_query_wms_401(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：根据 物料 SN_NO 查询返回 part_name, part_qty 
 * 
 * 
 */
	declare 
		jsondatas json;
		_sn_no text;
		_new_sn_no	text;
		tmp_json json[];
		qty_txt text;
		_invp_no text;
	    _supplier_no text;
		_is_hold_to_use bool;
		_sn_status	text;
		_mr_no		text;
		_part_no 	text;
		_sr_dlv_h_id	text;
		_ea_no		text;
		_workshop_no text;
		_cf_workshop_no text;
		_cf_sn_no	text;
		_cf_crt_time text;
		_me_mtr_use_h_sap_type text;
		_send_produce_date text;
		_min_produce_date text;
	    _min_sn_invp_no text;
	    _yx_sn_no text;
        _Super_Hair bool; --超发状态值(默认为true、有超发未归还为false)
	    create_zhouqi text;
	   	end_zhouqi text;
		res 	returntype;
	begin
		jsondatas := json(datas);
		raise notice '%',jsondatas;
	
		jsondatas := json(jsondatas->'datas'->0);
		_mr_no := jsondatas->>'mr_no';
		_sn_no := jsondatas->>'sn_no';
		_supplier_no := jsondatas->>'supplier_no';
		_Super_Hair:=true; --超发状态值
		--select array_agg(row_to_json(tmp)) into tmp_json 
		--from (select part_no,part_name,part_spec,part_qty,part_qty as part_qty_send from wm_sn where sn_no=_sn_no) tmp;
		select part_qty::text,invp_no,is_hold_to_use,sn_status,part_no,sr_dlv_h_id into qty_txt,_invp_no,_is_hold_to_use,_sn_status,_part_no,_sr_dlv_h_id from wm_sn where sn_no =_sn_no;
		--判断物料是否已检验合格
		_new_sn_no=substring(_sn_no,1,1);
		--期初条码、其它入库条码、退料条码过滤不验证是否合格
		if(_new_sn_no not in('Q','QT','T')) then
			--检验单时间大于2022-07-25就校验是否合格
			if exists(select 1 from qm_si_lot_h where part_no = _part_no and delivery_order_no = _sr_dlv_h_id and crt_time > '2022-07-25' limit 1) then
				if not exists(select 1 from qm_si_lot_h h where h.part_no = _part_no and h.delivery_order_no = _sr_dlv_h_id and h.si_lot_h_rmk06 = 'Y' and h.si_lot_h_status='5B8A60055F0200004DFF' and h.si_conclusion_name in('合格','特采')) then
					res := row('false',concat('条码:',_sn_no,' 物料:',_part_no,'，品保还未检验或者判定不合格不能发料！！！'));
					return to_json(res);
				end if;
			end if;
			
		end if;
		
		-----------校验物料是否有超发，如果有则提示先扣除超发账
		--获取领料单线别
--			select bb.me_mtr_use_b_rmk12  into _ea_no  
--			from me_mtr_use_h mm 
--			left join me_mtr_use_b bb on mm.me_mtr_use_h_id = bb.me_mtr_use_h_id 
--			where mm.me_mtr_use_h_no=_mr_no
--		    limit 1;
--		   --获取归属车间
--		   select e.ea_type3 into _workshop_no from ea e where e.ea_no ||'   '||e.ea_name =_ea_no ;
--		  --如果车间等于空值就使用产线
--		  if (_workshop_no is null or _workshop_no = '') then
--		  	_workshop_no := _ea_no;
--		  end if;
--		 
--		 --如果车间和产线都等于空值，就去获取非生产领料单的部门
--		 if (_ea_no is null or _ea_no = '' and _workshop_no is null or _workshop_no = '') then
--		 	select mm.me_mtr_use_h_dept_no into _workshop_no  
--			from me_mtr_use_h mm 
--			left join me_mtr_use_b bb on mm.me_mtr_use_h_id = bb.me_mtr_use_h_id 
--			where mm.me_mtr_use_h_no=_mr_no
--		    limit 1;
--		 end if;
--		----获取超发物料车间
--		select cf.workshop_no,cf.sn_no,cf.crt_time into _cf_workshop_no,_cf_sn_no,_cf_crt_time from szjy_wm_sn_issue_chaofa cf where cf.part_no = _part_no and cf.workshop_no = _workshop_no and cf.part_qty > 0 order by cf.crt_time limit 1;
--		--判定同车间先试用超发扣账
--		if not exists(select 1 from szjy_wm_sn_issue_chaofa cf where cf.part_no = _part_no and cf.sn_no = _sn_no and cf.workshop_no = _workshop_no and cf.part_qty > 0 limit 1) then
--			if(_cf_workshop_no = _workshop_no) then
--				res := row('false',concat('请先将此物料:',_part_no,' 的超发数量扣账发料完，再做正常发料!!!'));
--				return to_json(res);
--			end if;
--		end if;
		
		-----------------------------------------------------------------------------------------------------------------------------------
		
		--如果条码已上架，则不能发料必须先下架
		if (_invp_no != null or _invp_no != '') then
			res := row('false',concat('条码:',_sn_no,' 已上架:',_invp_no,'，如要发料请先下架！！！'));
			return to_json(res);
		end if;
		
		--领料单类型=报废，不管控冻结
		select me_mtr_use_h_sap_type into _me_mtr_use_h_sap_type from me_mtr_use_h hm where hm.me_mtr_use_h_no = _mr_no;
		if (_me_mtr_use_h_sap_type in('19')) then
		
		else
			if (_is_hold_to_use) then
				res := row('false',concat('条码:',_sn_no,' 已冻结','，禁止发料！！！'));
				return to_json(res);
			end if;
			
			--条码状态=作废，则不能发料
			if (_sn_status = '130') then
				res := row('false',concat('条码:',_sn_no,' 已作废','，禁止发料！！！'));
				return to_json(res);
			end if;
		
			--判断物料是否开启先进先出管控
			if exists(select 1 from pd_part pp where pp.is_fifo =true and pp.part_no =_part_no)then 
					--仓库发料先进先出控制---------------------
				if coalesce(_supplier_no,'') = '' then --供应商编号变量值为空，则执行以下脚本
				
					/*select concat(date_part('year',date(ws.produce_date)),lpad(cast(case when EXTRACT(DOW FROM ws.produce_date)=0 then
					date_part('week',date(ws.produce_date)+1) else date_part('week',date(ws.produce_date)) end as text),2,'0')) 
					into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;*/
					select af_sys_calc_week(ws.produce_date) into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;
				    
                    --2025.4.2 应仓库李辉的需求，超发扣账的物料不检查先进先出
	                if  exists (SELECT 1 FROM szjy_wm_sn_issue_chaofa cf  WHERE cf.sn_no = _sn_no AND cf.part_qty > 0)then
	                           _Super_Hair:=false;
	                end if;
			  

					create temp table t_is_fifo_tmp as 
					select ws.sn_no as yx_sn_no,
					--concat(date_part('year',date(ws.produce_date)),lpad(cast(case when EXTRACT(DOW FROM ws.produce_date)=0 then
					--date_part('week',date(ws.produce_date)+1) else date_part('week',date(ws.produce_date)) end as text),2,'0')) as min_produce_date,
					af_sys_calc_week(ws.produce_date) as min_produce_date,
					date(date_trunc('week',ws.produce_date))-1 as create_zhouqi,
					date(date_trunc('week',ws.produce_date))+5 as end_zhouqi,
					case when ws.invp_no is null then ' ' when ws.invp_no ='' then ' ' else ws.invp_no end as min_sn_invp_no
					--into _yx_sn_no,_min_produce_date,create_zhouqi,end_zhouqi,_min_sn_invp_no
					from wm_sn ws 
					where ws.part_no =_part_no
					and ws.part_qty > 0
					and not exists (select 1 from me_mtr_use_sn_part t where t.sn_no = ws.sn_no  )--变量_sn_no
					and exists (select 1 from wm_invp wi where wi.invp_no = ws.invp_no)  --不要关联库位
					and not exists (select 1 from szjy_wm_sn_issue_chaofa cf where cf.sn_no = ws.sn_no)
					and ws.sn_status = '110'
					order by ws.produce_date limit 100;
				
					select a.yx_sn_no,a.min_produce_date,a.create_zhouqi,a.end_zhouqi,a.min_sn_invp_no
						   into _yx_sn_no,_min_produce_date,create_zhouqi,end_zhouqi,_min_sn_invp_no
					from t_is_fifo_tmp a limit 1;
				
					
				     --2025.4.2 修改后新增_Super_Hair 
					if (cast(_send_produce_date as int) > cast(_min_produce_date as int) and _Super_Hair) then 
						 
						res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';周期：'||_min_produce_date||';周期开始日:'||create_zhouqi||';周期结束日:'||end_zhouqi||';当前库位:'||_min_sn_invp_no);
						return to_json(res);
						
					end if;

                  --2025.4.2 修改前逻辑
                  -- if (cast(_send_produce_date as int) > cast(_min_produce_date as int)) then 
--						 
--						res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';周期：'||_min_produce_date||';周期开始日:'||create_zhouqi||';周期结束日:'||end_zhouqi||';当前库位:'||_min_sn_invp_no);
--						return to_json(res);
--						
--					end if;
			
				else --则执行用供应商编号查询条码
				
					/*select concat(date_part('year',date(ws.produce_date)),lpad(cast(case when EXTRACT(DOW FROM ws.produce_date)=0 then
					date_part('week',date(ws.produce_date)+1) else date_part('week',date(ws.produce_date)) end as text),2,'0')) 
					into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;*/

					select af_sys_calc_week(ws.produce_date) into _send_produce_date from wm_sn ws where ws.sn_no =_sn_no;

                    --2025.4.2 应仓库李辉的需求，超发扣账的物料不检查先进先出
	                if  exists (SELECT 1 FROM szjy_wm_sn_issue_chaofa cf  WHERE cf.sn_no = _sn_no AND cf.part_qty > 0)then
	                           _Super_Hair:=false;
	                end if;
					
					/*select ws.sn_no ,concat(date_part('year',date(ws.produce_date)),lpad(cast(case when EXTRACT(DOW FROM ws.produce_date)=0 then
					date_part('week',date(ws.produce_date)+1) else date_part('week',date(ws.produce_date)) end as text),2,'0')),
					date(date_trunc('week',ws.produce_date))-1,date(date_trunc('week',ws.produce_date))+5,
					case when ws.invp_no is null then ' ' when ws.invp_no ='' then ' ' else ws.invp_no end
					into _yx_sn_no,_min_produce_date,create_zhouqi,end_zhouqi,_min_sn_invp_no
					from wm_sn ws 
					where ws.part_no =_part_no
					and ws.supplier_no = lower(_supplier_no)
					and ws.part_qty > 0
					and not exists (select 1 from me_mtr_use_sn_part t where t.sn_no = ws.sn_no  )
					and exists (select 1 from wm_invp wi where wi.invp_no = ws.invp_no)  --不要关联库位
					and not exists (select 1 from szjy_wm_sn_issue_chaofa cf where cf.sn_no = ws.sn_no)
					and ws.sn_status = '110'
					order by ws.produce_date limit 1;*/
				
					--改用临时表 2024.1.10
				
					create temp table t_is_fifo_tmp_sr as 
					select ws.sn_no as yx_sn_no,
					--concat(date_part('year',date(ws.produce_date)),lpad(cast(case when EXTRACT(DOW FROM ws.produce_date)=0 then
					--date_part('week',date(ws.produce_date)+1) else date_part('week',date(ws.produce_date)) end as text),2,'0')) as min_produce_date,
					af_sys_calc_week(ws.produce_date) as min_produce_date,
					date(date_trunc('week',ws.produce_date))-1 as create_zhouqi,
					date(date_trunc('week',ws.produce_date))+5 as end_zhouqi,
					case when ws.invp_no is null then ' ' when ws.invp_no ='' then ' ' else ws.invp_no end as min_sn_invp_no
					--into _yx_sn_no,_min_produce_date,create_zhouqi,end_zhouqi,_min_sn_invp_no
					from wm_sn ws 
					where ws.part_no =_part_no
					and ws.supplier_no = lower(_supplier_no)
					and ws.part_qty > 0
					and not exists (select 1 from me_mtr_use_sn_part t where t.sn_no = ws.sn_no  )--变量_sn_no
					and exists (select 1 from wm_invp wi where wi.invp_no = ws.invp_no)  --不要关联库位
					and not exists (select 1 from szjy_wm_sn_issue_chaofa cf where cf.sn_no = ws.sn_no)
					and ws.sn_status = '110'
					order by ws.produce_date limit 100;
				
					select a.yx_sn_no,a.min_produce_date,a.create_zhouqi,a.end_zhouqi,a.min_sn_invp_no
						   into _yx_sn_no,_min_produce_date,create_zhouqi,end_zhouqi,_min_sn_invp_no
					from t_is_fifo_tmp_sr a limit 1;
					
				
					
				 
				    --2025.4.2 修改后新增_Super_Hair 
					if  ( (_send_produce_date  > _min_produce_date) and  _Super_Hair)then 
						 
						res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';周期：'||_min_produce_date||';周期开始日:'||create_zhouqi||';周期结束日:'||end_zhouqi||';当前库位:'||_min_sn_invp_no);
						return to_json(res);
						
					end if;
                  
                    --2025.4.2 修改前逻辑
--                  if  (_send_produce_date  > _min_produce_date)then 
--						 
--						res := row('false','品号:'||_part_no||',已启用先进先出管控！请先发条码编号:'||_yx_sn_no||';周期：'||_min_produce_date||';周期开始日:'||create_zhouqi||';周期结束日:'||end_zhouqi||';当前库位:'||_min_sn_invp_no);
--						return to_json(res);
--						
--					end if;
			
				end if;  --判断结束	
			end if;
		end if;
	
	
		--如果有填供应商编号，则判断条码编号是否存在
		if coalesce(_supplier_no,'') <> '' then
			if not exists (select 1 from wm_sn ws where ws.sn_no = _sn_no and upper(ws.supplier_no) = _supplier_no ) then
			res := row('false',concat('条码编号:',_sn_no,'的供应商不匹配','，禁止发料！！！'));
			return to_json(res);
			end if;
		end if;
	
		if strpos(qty_txt,'.')>0 then	
			select array_agg(row_to_json(tmp)) into tmp_json 
			from (select part_no,part_name,null as part_spec,part_qty,rtrim(rtrim(part_qty::text,'0'),'.')::numeric as part_qty_send from wm_sn where sn_no=_sn_no) tmp;
		else
			select array_agg(row_to_json(tmp)) into tmp_json 
			from (select part_no,part_name,null as part_spec,part_qty,part_qty as part_qty_send from wm_sn where sn_no=_sn_no) tmp;
		end if;
		
		return json_build_object('successful',true,'msg','查询成功','datas',tmp_json);

	END;
$function$
;


```sql
-- DROP FUNCTION public.af_query_wms_314(varchar);

CREATE OR REPLACE FUNCTION public.af_query_wms_314(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：根据 领料单号 查询明细 返回 part_no, part_name, part_spec,part_qty 
 * 
 * 
 */
	declare 
		jsondatas json;
		_mr_no text;
		tmp_json json[];
		_me_mtr_use_h_rmk7 text; --Y 取消 N 正常
		res		returntype;
	begin
		jsondatas := json(datas);
		raise notice '%',jsondatas;
	
		jsondatas := json(jsondatas->'datas'->0);
		_mr_no := jsondatas->>'mr_no';
	
	
		--判断领料单是否取消，如果取消则不能发料
		select me_mtr_use_h_rmk7 into _me_mtr_use_h_rmk7 from me_mtr_use_h h where h.me_mtr_use_h_id = _mr_no;
		if (_me_mtr_use_h_rmk7 = 'Y') then
			res := row('false',concat('领料单号:',_mr_no,' SAP,已做取消不能发料！！！'));
			return to_json(res);
		end if;
	  
        --2025.6.18 heguangpin 新增条码过滤，委外五标签生成条码 and coalesce(rt.sign,'') <>'w'
		select array_agg(row_to_json(tmp)) into tmp_json 
		from (select t1.part_no,coalesce (t1.part_qty_req,0) as part_qty_req,coalesce (t2.part_qty_real,0) as part_qty_real,coalesce (t2.part_qty_real,0) - coalesce (t1.part_qty_req,0) as diff_qty, t1.part_name
				from(select  mb.part_name,sum(mb.part_qty_req) as part_qty_req,mb.part_no from me_mtr_use_h mh
						left join me_mtr_use_b mb on mb.me_mtr_use_h_id =mh.me_mtr_use_h_id 
						where mh.me_mtr_use_h_no =_mr_no
						group by mb.part_name,mb.part_no) t1
				left join (select coalesce (sum(rt.part_qty),0) as part_qty_real,rt.part_no 
						from me_mtr_use_h mh2
				    	left join me_mtr_use_sn_part rt on rt.me_mtr_use_h_id = mh2.me_mtr_use_h_id
						where  mh2.me_mtr_use_h_no =_mr_no and coalesce(rt.sign,'') <>'w'
						group by rt.part_no ) t2 on t2.part_no=t1.part_no 
				--where coalesce (t2.part_qty_real,0) - coalesce (t1.part_qty_req,0)<=0
				order by t1.part_no 
			) tmp;
			
		return json_build_object('successful',true,'msg','查询成功','datas',tmp_json);
		
		--return json_build_object('successful',true,'msg','查询成功','datas',tmp_json);

	END;
$function$
;
